<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>Application-Group</key>
	<string>AirPort</string>
	<key>BuildMachineOSBuild</key>
	<string>23A344014</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>English</string>
	<key>CFBundleDisplayName</key>
	<string>Install macOS Tahoe Beta</string>
	<key>CFBundleExecutable</key>
	<string>InstallAssistant_springboard</string>
	<key>CFBundleGetInfoString</key>
	<string>Install macOS Tahoe Beta, Copyright © 2007–2025 Apple Inc. All rights reserved.</string>
	<key>CFBundleIconFile</key>
	<string>InstallAssistant</string>
	<key>CFBundleIdentifier</key>
	<string>com.apple.InstallAssistant.Seed.macOS26Seed</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>Install mac OS</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>21.0.01</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleSupportedPlatforms</key>
	<array>
		<string>MacOSX</string>
	</array>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLName</key>
			<string>Open Install OS X URL</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>x-install-osx-assistant</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>21001</string>
	<key>DTCompiler</key>
	<string>com.apple.compilers.llvm.clang.1_0</string>
	<key>DTPlatformBuild</key>
	<string>25A5295d</string>
	<key>DTPlatformName</key>
	<string>macosx</string>
	<key>DTPlatformVersion</key>
	<string>26.0</string>
	<key>DTSDKBuild</key>
	<string>25A5295d</string>
	<key>DTSDKName</key>
	<string>macosx26.0.internal</string>
	<key>DTXcode</key>
	<string>1700</string>
	<key>DTXcodeBuild</key>
	<string>17A6231r</string>
	<key>LSApplicationCategoryType</key>
	<string>public.app-category.utilities</string>
	<key>LSHasLocalizedDisplayName</key>
	<true/>
	<key>LSMinimumSystemVersion</key>
	<string>10.15</string>
	<key>MinimumOSVersion</key>
	<string>10.15</string>
	<key>NSMainNibFile</key>
	<string>MainMenu</string>
	<key>NSPrincipalClass</key>
	<string>NSApplication</string>
	<key>NSSupportsAutomaticGraphicsSwitching</key>
	<true/>
	<key>ProductPageIconFile</key>
	<string>ProductPageIcon.icns</string>
</dict>
</plist>

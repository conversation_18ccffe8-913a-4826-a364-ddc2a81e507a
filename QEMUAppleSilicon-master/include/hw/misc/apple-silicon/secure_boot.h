/*
 * Apple Silicon Secure Boot Infrastructure
 * 
 * Copyright (c) 2024 QEMU Apple Silicon Contributors
 * 
 * This work is licensed under the terms of the GNU GPL, version 2 or later.
 * See the COPYING file in the top-level directory.
 */

#ifndef HW_MISC_APPLE_SILICON_SECURE_BOOT_H
#define HW_MISC_APPLE_SILICON_SECURE_BOOT_H

#include "qemu/osdep.h"
#include "hw/sysbus.h"
#include "hw/arm/apple-silicon/dtb.h"

/**
 * Apple Silicon Secure Boot Controller
 * 
 * This device emulates the secure boot infrastructure found in Apple Silicon
 * chips, including IMG4 verification, trust cache validation, and boot
 * manifest verification required for macOS boot.
 */

// Public API functions
SysBusDevice *apple_secure_boot_create(DTBNode *node);

bool apple_secure_boot_verify_img4(SysBusDevice *secure_boot_dev,
                                   const uint8_t *img4_data, size_t img4_size);

bool apple_secure_boot_validate_trustcache(SysBusDevice *secure_boot_dev,
                                           const uint8_t *trustcache_data,
                                           size_t trustcache_size);

// Secure boot status flags
#define SECURE_BOOT_FLAG_ENABLED        (1 << 0)
#define SECURE_BOOT_FLAG_DEVELOPMENT    (1 << 1)
#define SECURE_BOOT_FLAG_PRODUCTION     (1 << 2)

// IMG4 verification flags
#define IMG4_FLAG_VERIFICATION_ENABLED  (1 << 0)
#define IMG4_FLAG_NONCE_REQUIRED        (1 << 1)

#endif /* HW_MISC_APPLE_SILICON_SECURE_BOOT_H */

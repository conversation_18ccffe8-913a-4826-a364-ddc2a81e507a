/*
 * Apple Silicon NVRAM Controller for macOS
 * 
 * Copyright (c) 2024 QEMU Apple Silicon Contributors
 * 
 * This work is licensed under the terms of the GNU GPL, version 2 or later.
 * See the COPYING file in the top-level directory.
 */

#ifndef HW_NVRAM_APPLE_NVRAM_MACOS_H
#define HW_NVRAM_APPLE_NVRAM_MACOS_H

#include "qemu/osdep.h"
#include "hw/sysbus.h"
#include "hw/arm/apple-silicon/dtb.h"

/**
 * Apple Silicon NVRAM Controller for macOS
 * 
 * This device emulates the NVRAM controller found in Apple Silicon chips,
 * providing persistent storage for macOS boot variables, system settings,
 * and security configuration.
 */

// Public API functions
SysBusDevice *apple_nvram_macos_create(DTBNode *node, uint32_t nvram_size);

// Common macOS NVRAM variable names
#define NVRAM_VAR_BOOT_ARGS             "boot-args"
#define NVRAM_VAR_BOOT_VOLUME           "boot-volume"
#define NVRAM_VAR_AUTO_BOOT             "auto-boot"
#define NVRAM_VAR_BOOT_DELAY            "boot-delay"
#define NVRAM_VAR_PREV_LANG_KBD         "prev-lang:kbd"
#define NVRAM_VAR_SYSTEM_AUDIO_VOLUME   "SystemAudioVolume"
#define NVRAM_VAR_STARTUP_MUTE          "StartupMute"
#define NVRAM_VAR_ONE_TIME_BOOT_COMMAND "one-time-boot-command"
#define NVRAM_VAR_BOOT_NONCE            "boot-nonce"
#define NVRAM_VAR_SECURE_BOOT_ENABLED   "secure-boot-enabled"
#define NVRAM_VAR_SIP_STATUS            "csr-active-config"

#endif /* HW_NVRAM_APPLE_NVRAM_MACOS_H */

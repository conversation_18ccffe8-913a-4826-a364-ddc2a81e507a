amd64-debian-cross-container:
  extends: .container_job_template
  stage: containers
  variables:
    NAME: debian-amd64-cross

amd64-debian-user-cross-container:
  extends: .container_job_template
  stage: containers
  variables:
    NAME: debian-all-test-cross

amd64-debian-legacy-cross-container:
  extends: .container_job_template
  stage: containers
  variables:
    NAME: debian-legacy-test-cross

arm64-debian-cross-container:
  extends: .container_job_template
  stage: containers
  variables:
    NAME: debian-arm64-cross

armhf-debian-cross-container:
  extends: .container_job_template
  stage: containers
  variables:
    NAME: debian-armhf-cross

hexagon-cross-container:
  extends: .container_job_template
  stage: containers
  variables:
    NAME: debian-hexagon-cross

loongarch-debian-cross-container:
  extends: .container_job_template
  stage: containers
  variables:
    NAME: debian-loongarch-cross

i686-debian-cross-container:
  extends: .container_job_template
  stage: containers
  variables:
    NAME: debian-i686-cross

mips64el-debian-cross-container:
  extends: .container_job_template
  stage: containers
  variables:
    NAME: debian-mips64el-cross

mipsel-debian-cross-container:
  extends: .container_job_template
  stage: containers
  variables:
    NAME: debian-mipsel-cross

ppc64el-debian-cross-container:
  extends: .container_job_template
  stage: containers
  variables:
    NAME: debian-ppc64el-cross

riscv64-debian-cross-container:
  extends: .container_job_template
  stage: containers
  # as we are currently based on 'sid/unstable' we may break so...
  allow_failure: true
  variables:
    NAME: debian-riscv64-cross
    QEMU_JOB_OPTIONAL: 1

s390x-debian-cross-container:
  extends: .container_job_template
  stage: containers
  variables:
    NAME: debian-s390x-cross

tricore-debian-cross-container:
  extends: .container_job_template
  stage: containers
  variables:
    NAME: debian-tricore-cross

xtensa-debian-cross-container:
  extends: .container_job_template
  variables:
    NAME: debian-xtensa-cross

win64-fedora-cross-container:
  extends: .container_job_template
  variables:
    NAME: fedora-win64-cross

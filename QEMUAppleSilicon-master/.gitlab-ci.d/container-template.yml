.container_job_template:
  extends: .base_job_template
  image: docker:latest
  stage: containers
  services:
    - docker:dind
  before_script:
    - export TAG="$CI_REGISTRY_IMAGE/qemu/$NAME:$QEMU_CI_CONTAINER_TAG"
    # Always ':latest' because we always use upstream as a common cache source
    - export COMMON_TAG="$CI_REGISTRY/qemu-project/qemu/qemu/$NAME:latest"
    - docker login $CI_REGISTRY -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD"
    - until docker info; do sleep 1; done
  script:
    - echo "TAG:$TAG"
    - echo "COMMON_TAG:$COMMON_TAG"
    - docker build --tag "$TAG" --cache-from "$TAG" --cache-from "$COMMON_TAG"
      --build-arg BUILDKIT_INLINE_CACHE=1
      -f "tests/docker/dockerfiles/$NAME.docker" "."
    - docker push "$TAG"
  after_script:
    - docker logout

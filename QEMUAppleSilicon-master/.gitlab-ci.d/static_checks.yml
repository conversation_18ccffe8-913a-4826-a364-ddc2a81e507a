check-patch:
  extends: .base_job_template
  stage: build
  image: python:3.10-alpine
  needs: []
  script:
    - .gitlab-ci.d/check-patch.py
  variables:
    GIT_DEPTH: 1000
    QEMU_JOB_ONLY_FORKS: 1
  before_script:
    - apk -U add git perl
  allow_failure: true

check-dco:
  extends: .base_job_template
  stage: build
  image: python:3.10-alpine
  needs: []
  script: .gitlab-ci.d/check-dco.py
  variables:
    GIT_DEPTH: 1000
  before_script:
    - apk -U add git

check-python-minreqs:
  extends: .base_job_template
  stage: test
  image: $CI_REGISTRY_IMAGE/qemu/python:$QEMU_CI_CONTAINER_TAG
  script:
    - make -C python check-minreqs
  variables:
    GIT_DEPTH: 1
  needs:
    job: python-container

check-python-tox:
  extends: .base_job_template
  stage: test
  image: $CI_REGISTRY_IMAGE/qemu/python:$QEMU_CI_CONTAINER_TAG
  script:
    - make -C python check-tox
  variables:
    GIT_DEPTH: 1
    QEMU_TOX_EXTRA_ARGS: --skip-missing-interpreters=false
    QEMU_JOB_OPTIONAL: 1
  needs:
    job: python-container

check-rust-tools-nightly:
  extends: .base_job_template
  stage: test
  image: $CI_REGISTRY_IMAGE/qemu/fedora-rust-nightly:$QEMU_CI_CONTAINER_TAG
  script:
    - source scripts/ci/gitlab-ci-section
    - section_start test "Running Rust code checks"
    - cd build
    - pyvenv/bin/meson devenv -w ../rust ${CARGO-cargo} fmt --check
    - make clippy
    - make rustdoc
    - section_end test
  variables:
    GIT_DEPTH: 1
  allow_failure: true
  needs:
    - job: build-system-fedora-rust-nightly
      artifacts: true
  artifacts:
    when: on_success
    expire_in: 2 days
    paths:
      - rust/target/doc

check-build-units:
  extends: .base_job_template
  stage: build
  image: $CI_REGISTRY_IMAGE/qemu/debian:$QEMU_CI_CONTAINER_TAG
  needs:
    job: amd64-debian-container
  before_script:
    - source scripts/ci/gitlab-ci-section
    - section_start setup "Install Tools"
    - apt install --assume-yes --no-install-recommends jq
    - section_end setup
  script:
    - mkdir build
    - cd build
    - section_start configure "Running configure"
    - ../configure
    - cd ..
    - section_end configure
    - section_start analyse "Analyse"
    - .gitlab-ci.d/check-units.py build/compile_commands.json
    - section_end analyse

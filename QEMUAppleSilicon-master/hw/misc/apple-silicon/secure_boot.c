/*
 * Apple Silicon Secure Boot Infrastructure
 * 
 * Copyright (c) 2024 QEMU Apple Silicon Contributors
 * 
 * This work is licensed under the terms of the GNU GPL, version 2 or later.
 * See the COPYING file in the top-level directory.
 */

#include "qemu/osdep.h"
#include "qemu/log.h"
#include "qemu/module.h"
#include "hw/sysbus.h"
#include "hw/misc/apple-silicon/secure_boot.h"
#include "hw/arm/apple-silicon/dtb.h"

#define TYPE_APPLE_SECURE_BOOT "apple.secure-boot"
OBJECT_DECLARE_SIMPLE_TYPE(AppleSecureBootState, APPLE_SECURE_BOOT)

struct AppleSecureBootState {
    SysBusDevice parent_obj;
    
    MemoryRegion iomem;
    
    // Secure boot state
    bool secure_boot_enabled;
    bool development_mode;
    bool production_status;
    uint64_t boot_nonce;
    uint8_t boot_manifest_hash[48];  // SHA-384 hash
    
    // IMG4 verification state
    bool img4_verification_enabled;
    uint32_t img4_nonce;
    
    // Trust cache state
    bool trustcache_enabled;
    uint64_t trustcache_base;
    uint32_t trustcache_size;
};

// Secure boot register offsets
#define SECURE_BOOT_STATUS      0x0000
#define SECURE_BOOT_CONTROL     0x0004
#define SECURE_BOOT_NONCE       0x0008
#define SECURE_BOOT_HASH_BASE   0x0010
#define IMG4_CONTROL            0x0050
#define IMG4_NONCE              0x0054
#define TRUSTCACHE_BASE         0x0060
#define TRUSTCACHE_SIZE         0x0068

static uint64_t apple_secure_boot_read(void *opaque, hwaddr addr, unsigned size)
{
    AppleSecureBootState *s = APPLE_SECURE_BOOT(opaque);
    uint64_t value = 0;
    
    switch (addr) {
    case SECURE_BOOT_STATUS:
        value = (s->secure_boot_enabled ? 1 : 0) |
                (s->development_mode ? 2 : 0) |
                (s->production_status ? 4 : 0);
        break;
    case SECURE_BOOT_NONCE:
        value = s->boot_nonce;
        break;
    case IMG4_CONTROL:
        value = s->img4_verification_enabled ? 1 : 0;
        break;
    case IMG4_NONCE:
        value = s->img4_nonce;
        break;
    case TRUSTCACHE_BASE:
        value = s->trustcache_base;
        break;
    case TRUSTCACHE_SIZE:
        value = s->trustcache_size;
        break;
    default:
        if (addr >= SECURE_BOOT_HASH_BASE && addr < SECURE_BOOT_HASH_BASE + 48) {
            // Return boot manifest hash
            uint32_t offset = addr - SECURE_BOOT_HASH_BASE;
            if (offset + size <= 48) {
                memcpy(&value, &s->boot_manifest_hash[offset], size);
            }
        } else {
            qemu_log_mask(LOG_UNIMP,
                          "Secure Boot: Unimplemented read @ 0x" HWADDR_FMT_plx "\n",
                          addr);
        }
        break;
    }
    
    return value;
}

static void apple_secure_boot_write(void *opaque, hwaddr addr, uint64_t value,
                                    unsigned size)
{
    AppleSecureBootState *s = APPLE_SECURE_BOOT(opaque);
    
    switch (addr) {
    case SECURE_BOOT_CONTROL:
        s->secure_boot_enabled = (value & 1) != 0;
        s->development_mode = (value & 2) != 0;
        s->production_status = (value & 4) != 0;
        break;
    case SECURE_BOOT_NONCE:
        s->boot_nonce = value;
        break;
    case IMG4_CONTROL:
        s->img4_verification_enabled = (value & 1) != 0;
        break;
    case IMG4_NONCE:
        s->img4_nonce = value;
        break;
    case TRUSTCACHE_BASE:
        s->trustcache_base = value;
        break;
    case TRUSTCACHE_SIZE:
        s->trustcache_size = value;
        break;
    default:
        if (addr >= SECURE_BOOT_HASH_BASE && addr < SECURE_BOOT_HASH_BASE + 48) {
            // Set boot manifest hash
            uint32_t offset = addr - SECURE_BOOT_HASH_BASE;
            if (offset + size <= 48) {
                memcpy(&s->boot_manifest_hash[offset], &value, size);
            }
        } else {
            qemu_log_mask(LOG_UNIMP,
                          "Secure Boot: Unimplemented write @ 0x" HWADDR_FMT_plx
                          " value: 0x" HWADDR_FMT_plx "\n",
                          addr, value);
        }
        break;
    }
}

static const MemoryRegionOps apple_secure_boot_ops = {
    .read = apple_secure_boot_read,
    .write = apple_secure_boot_write,
    .endianness = DEVICE_LITTLE_ENDIAN,
    .valid = {
        .min_access_size = 4,
        .max_access_size = 8,
    },
};

static void apple_secure_boot_realize(DeviceState *dev, Error **errp)
{
    AppleSecureBootState *s = APPLE_SECURE_BOOT(dev);
    SysBusDevice *sbd = SYS_BUS_DEVICE(dev);
    
    memory_region_init_io(&s->iomem, OBJECT(dev), &apple_secure_boot_ops, s,
                          TYPE_APPLE_SECURE_BOOT, 0x1000);
    sysbus_init_mmio(sbd, &s->iomem);
    
    // Initialize secure boot state for macOS compatibility
    s->secure_boot_enabled = true;
    s->development_mode = false;  // Production mode for macOS
    s->production_status = true;
    s->boot_nonce = 0x1234567890ABCDEFULL;
    s->img4_verification_enabled = true;
    s->img4_nonce = 0x12345678;
    s->trustcache_enabled = true;
    s->trustcache_base = 0;  // Will be set by bootloader
    s->trustcache_size = 0;
    
    // Initialize boot manifest hash (fake but consistent)
    memset(s->boot_manifest_hash, 0xAB, sizeof(s->boot_manifest_hash));
}

static void apple_secure_boot_class_init(ObjectClass *klass, void *data)
{
    DeviceClass *dc = DEVICE_CLASS(klass);
    
    dc->realize = apple_secure_boot_realize;
    dc->desc = "Apple Silicon Secure Boot Controller";
}

static const TypeInfo apple_secure_boot_info = {
    .name = TYPE_APPLE_SECURE_BOOT,
    .parent = TYPE_SYS_BUS_DEVICE,
    .instance_size = sizeof(AppleSecureBootState),
    .class_init = apple_secure_boot_class_init,
};

static void apple_secure_boot_register_types(void)
{
    type_register_static(&apple_secure_boot_info);
}

type_init(apple_secure_boot_register_types)

// Public API for creating secure boot controller
SysBusDevice *apple_secure_boot_create(DTBNode *node)
{
    DeviceState *dev;
    SysBusDevice *sbd;
    DTBProp *prop;
    uint64_t *reg;
    
    dev = qdev_new(TYPE_APPLE_SECURE_BOOT);
    sbd = SYS_BUS_DEVICE(dev);
    
    if (node) {
        prop = dtb_find_prop(node, "reg");
        if (prop) {
            reg = (uint64_t *)prop->data;
            sysbus_realize_and_map(sbd, &error_fatal, reg[0]);
        } else {
            sysbus_realize_and_map(sbd, &error_fatal, 0x23B700000ULL);
        }
        
        // Add device tree properties for macOS
        dtb_set_prop_str(node, "compatible", "apple,secure-boot");
        dtb_set_prop_str(node, "device_type", "secure-boot");
        dtb_set_prop_u32(node, "secure-boot-enabled", 1);
        dtb_set_prop_u32(node, "img4-verification-enabled", 1);
        dtb_set_prop_u32(node, "production-status", 1);
    } else {
        sysbus_realize_and_map(sbd, &error_fatal, 0x23B700000ULL);
    }
    
    return sbd;
}

// IMG4 verification simulation
bool apple_secure_boot_verify_img4(SysBusDevice *secure_boot_dev,
                                   const uint8_t *img4_data, size_t img4_size)
{
    // For emulation purposes, always return true
    // In real hardware, this would verify IMG4 signatures
    return true;
}

// Trust cache validation simulation  
bool apple_secure_boot_validate_trustcache(SysBusDevice *secure_boot_dev,
                                           const uint8_t *trustcache_data,
                                           size_t trustcache_size)
{
    // For emulation purposes, always return true
    // In real hardware, this would validate trust cache signatures
    return true;
}

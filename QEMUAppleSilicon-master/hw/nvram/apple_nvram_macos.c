/*
 * Apple Silicon NVRAM Controller for macOS
 * 
 * Copyright (c) 2024 QEMU Apple Silicon Contributors
 * 
 * This work is licensed under the terms of the GNU GPL, version 2 or later.
 * See the COPYING file in the top-level directory.
 */

#include "qemu/osdep.h"
#include "qemu/log.h"
#include "qemu/module.h"
#include "qemu/cutils.h"
#include "qapi/error.h"
#include "hw/sysbus.h"
#include "hw/qdev-properties.h"
#include "hw/nvram/apple_nvram_macos.h"
#include "hw/arm/apple-silicon/dtb.h"

#define TYPE_APPLE_NVRAM_MACOS "apple.nvram-macos"
OBJECT_DECLARE_SIMPLE_TYPE(AppleNVRAMMacOSState, APPLE_NVRAM_MACOS)

struct AppleNVRAMMacOSState {
    SysBusDevice parent_obj;
    
    MemoryRegion iomem;
    
    // NVRAM storage
    uint8_t *nvram_data;
    uint32_t nvram_size;
    bool nvram_dirty;
    
    // macOS-specific NVRAM variables
    char boot_args[512];
    char boot_volume[256];
    uint32_t auto_boot;
    uint32_t boot_delay;
    char prev_lang_kbd[32];
    uint32_t system_audio_volume;
    uint32_t startup_mute;
    char one_time_boot_command[256];
    
    // Security variables
    uint8_t boot_nonce[32];
    uint32_t secure_boot_enabled;
    uint32_t sip_status;
};

// NVRAM register offsets
#define NVRAM_DATA_REG      0x0000
#define NVRAM_ADDR_REG      0x0004
#define NVRAM_CONTROL_REG   0x0008
#define NVRAM_STATUS_REG    0x000C

// NVRAM control flags
#define NVRAM_CTRL_READ     (1 << 0)
#define NVRAM_CTRL_WRITE    (1 << 1)
#define NVRAM_CTRL_SYNC     (1 << 2)

// NVRAM status flags
#define NVRAM_STATUS_READY  (1 << 0)
#define NVRAM_STATUS_DIRTY  (1 << 1)

static void apple_nvram_macos_init_defaults(AppleNVRAMMacOSState *s)
{
    // Initialize default macOS NVRAM variables
    strncpy(s->boot_args, "", sizeof(s->boot_args) - 1);
    strncpy(s->boot_volume, "", sizeof(s->boot_volume) - 1);
    s->auto_boot = 1;
    s->boot_delay = 0;
    strncpy(s->prev_lang_kbd, "en:0", sizeof(s->prev_lang_kbd) - 1);
    s->system_audio_volume = 50;
    s->startup_mute = 0;
    strncpy(s->one_time_boot_command, "", sizeof(s->one_time_boot_command) - 1);
    
    // Security defaults
    memset(s->boot_nonce, 0, sizeof(s->boot_nonce));
    s->secure_boot_enabled = 1;
    s->sip_status = 0x10;  // SIP enabled
}

static void apple_nvram_macos_load_from_storage(AppleNVRAMMacOSState *s)
{
    // For now, just initialize with defaults
    // TODO: Add persistent storage support
    apple_nvram_macos_init_defaults(s);
}

static void apple_nvram_macos_save_to_storage(AppleNVRAMMacOSState *s)
{
    // For now, just mark as not dirty
    // TODO: Add persistent storage support
    s->nvram_dirty = false;
}

static uint64_t apple_nvram_macos_read(void *opaque, hwaddr addr, unsigned size)
{
    AppleNVRAMMacOSState *s = APPLE_NVRAM_MACOS(opaque);
    uint64_t value = 0;
    
    switch (addr) {
    case NVRAM_STATUS_REG:
        value = NVRAM_STATUS_READY;
        if (s->nvram_dirty) {
            value |= NVRAM_STATUS_DIRTY;
        }
        break;
    default:
        qemu_log_mask(LOG_UNIMP,
                      "Apple NVRAM: Unimplemented read @ 0x" HWADDR_FMT_plx "\n",
                      addr);
        break;
    }
    
    return value;
}

static void apple_nvram_macos_write(void *opaque, hwaddr addr, uint64_t value,
                                    unsigned size)
{
    AppleNVRAMMacOSState *s = APPLE_NVRAM_MACOS(opaque);
    
    switch (addr) {
    case NVRAM_CONTROL_REG:
        if (value & NVRAM_CTRL_SYNC) {
            apple_nvram_macos_save_to_storage(s);
        }
        break;
    default:
        qemu_log_mask(LOG_UNIMP,
                      "Apple NVRAM: Unimplemented write @ 0x" HWADDR_FMT_plx
                      " value: 0x" HWADDR_FMT_plx "\n",
                      addr, value);
        break;
    }
}

static const MemoryRegionOps apple_nvram_macos_ops = {
    .read = apple_nvram_macos_read,
    .write = apple_nvram_macos_write,
    .endianness = DEVICE_LITTLE_ENDIAN,
    .valid = {
        .min_access_size = 4,
        .max_access_size = 8,
    },
};

static void apple_nvram_macos_realize(DeviceState *dev, Error **errp)
{
    AppleNVRAMMacOSState *s = APPLE_NVRAM_MACOS(dev);
    SysBusDevice *sbd = SYS_BUS_DEVICE(dev);
    
    memory_region_init_io(&s->iomem, OBJECT(dev), &apple_nvram_macos_ops, s,
                          TYPE_APPLE_NVRAM_MACOS, 0x1000);
    sysbus_init_mmio(sbd, &s->iomem);
    
    // Allocate NVRAM storage
    if (s->nvram_size == 0) {
        s->nvram_size = 8192;  // Default 8KB NVRAM
    }
    s->nvram_data = g_malloc0(s->nvram_size);
    
    // Load NVRAM data from storage
    apple_nvram_macos_load_from_storage(s);
}

static void apple_nvram_macos_unrealize(DeviceState *dev)
{
    AppleNVRAMMacOSState *s = APPLE_NVRAM_MACOS(dev);
    
    // Save any pending changes
    apple_nvram_macos_save_to_storage(s);
    
    g_free(s->nvram_data);
}

// Properties removed for simplicity - using default values

static void apple_nvram_macos_class_init(ObjectClass *klass, void *data)
{
    DeviceClass *dc = DEVICE_CLASS(klass);
    
    dc->realize = apple_nvram_macos_realize;
    dc->unrealize = apple_nvram_macos_unrealize;
    dc->desc = "Apple Silicon NVRAM Controller for macOS";
}

static const TypeInfo apple_nvram_macos_info = {
    .name = TYPE_APPLE_NVRAM_MACOS,
    .parent = TYPE_SYS_BUS_DEVICE,
    .instance_size = sizeof(AppleNVRAMMacOSState),
    .class_init = apple_nvram_macos_class_init,
};

static void apple_nvram_macos_register_types(void)
{
    type_register_static(&apple_nvram_macos_info);
}

type_init(apple_nvram_macos_register_types)

// Public API for creating NVRAM controller
SysBusDevice *apple_nvram_macos_create(DTBNode *node, uint32_t nvram_size)
{
    DeviceState *dev;
    SysBusDevice *sbd;
    DTBProp *prop;
    uint64_t *reg;
    
    dev = qdev_new(TYPE_APPLE_NVRAM_MACOS);
    sbd = SYS_BUS_DEVICE(dev);
    
    // Set nvram_size directly since we removed properties
    if (nvram_size > 0) {
        AppleNVRAMMacOSState *s = APPLE_NVRAM_MACOS(dev);
        s->nvram_size = nvram_size;
    }
    
    if (node) {
        prop = dtb_find_prop(node, "reg");
        if (prop) {
            reg = (uint64_t *)prop->data;
            sysbus_realize_and_unref(sbd, &error_abort);
            sysbus_mmio_map(sbd, 0, reg[0]);
        } else {
            sysbus_realize_and_unref(sbd, &error_abort);
            sysbus_mmio_map(sbd, 0, 0x23D2B0000ULL);
        }
        
        // Add device tree properties for macOS
        dtb_set_prop_str(node, "compatible", "apple,nvram");
        dtb_set_prop_str(node, "device_type", "nvram");
        dtb_set_prop_u32(node, "nvram-size", nvram_size ? nvram_size : 8192);
        dtb_set_prop_u32(node, "nvram-version", 1);
    } else {
        sysbus_realize_and_unref(sbd, &error_abort);
        sysbus_mmio_map(sbd, 0, 0x23D2B0000ULL);
    }
    
    return sbd;
}
